{"cells": [{"cell_type": "markdown", "id": "9fd82fc5", "metadata": {}, "source": ["# 要点\n", "- 最后给llm的时候其实这里说的system_prompt排在了最后，感觉可有可无。\n", "- 最后给llm的时候context_promt是排在最前面的\n", "- 提示词精调过\n", "- 这个是主要是针对人文学神系统写的\n", "- 用了redis来进行持久化\n", "\n"]}, {"cell_type": "code", "execution_count": 177, "id": "08f3f865", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 已从Qdrant 6334端口加载向量数据到index\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_39188\\4269384171.py:9: UserWarning: Failed to obtain server version. Unable to check client-server compatibility. Set check_compatibility=False to skip version check.\n", "  qdrant_client = QdrantClient(\n"]}], "source": ["# 连接qdrant\n", "\n", "# 从本地Qdrant 6334端口加载已有向量数据到index\n", "from qdrant_client import QdrantClient\n", "from llama_index.vector_stores.qdrant import QdrantVectorStore\n", "from llama_index.core import StorageContext, VectorStoreIndex\n", "\n", "# 连接到本地Qdrant gRPC端口6334\n", "qdrant_client = QdrantClient(\n", "    host=\"localhost\",\n", "    port=6334,  # gRPC端口，比6333 HTTP端口性能更好\n", "    prefer_grpc=True,\n", "    timeout=10\n", ")\n", "\n", "\n", "# 从已有集合创建向量存储\n", "# 这个qdrant的warning总比崩溃好\n", "collection_name = \"course_materials\"  # 使用已存在的集合\n", "vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)\n", "storage_context = StorageContext.from_defaults(vector_store=vector_store)\n", "\n", "# 从Qdrant向量存储创建index（不重新写入数据）\n", "index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)\n", "\n", "print(\"✅ 已从Qdrant 6334端口加载向量数据到index\")"]}, {"cell_type": "markdown", "id": "ab9aed7e", "metadata": {}, "source": ["## 1️⃣ 导入 & 全局设置\n", "我们选用 OpenAI 作为 LLM；`Settings` 里统一配置，便于所有组件共享。"]}, {"cell_type": "code", "execution_count": 178, "id": "428641d2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 已设置 LLM 和 Embedding。\n", "\n"]}], "source": ["# =============================\n", "# 1) 基础导入与全局设置\n", "# =============================\n", "import os\n", "from llama_index.core import Settings, VectorStoreIndex, PromptTemplate\n", "from llama_index.core import SimpleDirectoryReader\n", "from llama_index.core.node_parser import SentenceSplitter\n", "from llama_index.llms.openai import OpenAI\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "from llama_index.core.chat_engine import SimpleChatEngine\n", "\n", "# 替换成你自己的 OpenAI API Key\n", "os.environ[\"OPENAI_API_KEY\"] = \"sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE\"\n", "# 如需自定义网关（如代理或 Azure），取消下面注释并替换\n", "os.environ[\"OPENAI_BASE_URL\"] = \"https://api.openai-proxy.org/v1\"\n", "\n", "\n", "\n", "# ➊ 配置全局设置（替代 ServiceContext）\n", "Settings.llm = OpenAI(\n", "    model=\"gpt-4o-mini\", \n", "    temperature=0.1,\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "Settings.embed_model = OpenAIEmbedding(\n", "    model=\"text-embedding-3-small\",\n", "    api_base=\"https://api.openai-proxy.org/v1\"\n", ")\n", "\n", "\n", "print(\"✅ 已设置 LLM 和 Embedding。\\n\")"]}, {"cell_type": "markdown", "id": "0af60d2c", "metadata": {}, "source": ["##  建立两个 Chat Engine（共享同一个 Memory）\n", "- **A：`condense_plus_question`**（对历史+本轮进行**问题凝练** ➜ 检索索引 ➜ 带聊天历史和检索到的文本进行答复）  \n", "- **B：`simple`**（**不检索**，直接与 LLM 聊天，但同样利用**共享内存**）\n", "\n", "### 在condense_question_plus模式下，给llm的内容是context_prompt_template + system_prompt + chat_history + query_str,所以其实system_prompt没有必要"]}, {"cell_type": "code", "execution_count": 179, "id": "1df2c78e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n", "\n"]}], "source": ["# =============================\n", "#  共享的 ChatSummaryMemoryBuffer, 持久化在redis里面，还没有持久在硬盘里面，别着急\n", "# =============================\n", "from llama_index.storage.chat_store.redis import RedisChatStore\n", "from llama_index.core.memory import ChatSummaryMemoryBuffer\n", "\n", "\n", "chat_store = RedisChatStore(redis_url=\"redis://localhost:6379\", ttl=3600)\n", "\n", "#ttl是所有存入chat_store的存活时间，单位是秒。注意，chat_store不能随便建立，这个耗时比较长\n", "\n", "custom_summary_prompt = \"\"\"你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。\n", "\"\"\"\n", "\n", "memory = ChatSummaryMemoryBuffer.from_defaults(\n", "    token_limit = 4000, # 测试用1000，真实用4000，后续估计还有调整，感觉根本用不完\n", "    llm=Settings.llm,  # 用同一个 LLM 进行摘要\n", "    chat_store=chat_store,\n", "    chat_store_key=\"condense_plus_chat\",\n", "    summarize_prompt=custom_summary_prompt\n", ")\n", "\n", "print(memory.summarize_prompt)"]}, {"cell_type": "code", "execution_count": 180, "id": "42a2d357", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 两个引擎均已就绪，且共享同一份 ChatSummaryMemoryBuffer。\n", "\n"]}], "source": ["# 两种聊天模式加上对应的提示词\n", "\n", "\n", "from llama_index.core.prompts import PromptTemplate\n", "\n", "# \"condense_question\"用的提示词\n", "new_condense_prompt = PromptTemplate(\n", "    \"你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\\n\"\n", "    \"注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\\n\"\n", "    \"=== 聊天历史 ===\\n\"\n", "    \"{chat_history}\\n\\n\"\n", "    \"=== 学生最新提出的问题 ===\\n\"\n", "    \"{question}\\n\\n\"\n", "    \"=== 改写后的独立问题 ===\\n\"\n", ")\n", "\n", "\n", "\n", "# 3. 自定义 context_prompt（整合检索内容和用户问题的核心提示词）\n", "custom_context_prompt = (\n", "    \"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\\n\\n\"\n", "    \"📚 **相关文档内容：**\\n\"\n", "    \"{context_str}\\n\\n\"\n", "    \"🎯 **回答要求：**\\n\"\n", "    \"1. 严格基于上述文档内容进行回答\\n\"\n", "    \"2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\\n\"\n", "    \"3. 回答要条理清晰，使用适当的emoji让内容更生动\\n\"\n", "    \"4. 请引用具体的文档内容来支撑你的回答\\n\\n\"\n", "    \"💡 **请基于以上文档和之前的对话历史来回答用户的问题。**\"\n", "     \"根据以上信息，请回答这个问题: {query_str}\\n\\n\" #这里放一个{query_str}可以但是可能不合适，不过我觉得是最佳实践 ---by James\n", "     \"====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\\n\\n\"\n", ")\n", "\n", "# 4. 创建 condense_plus_context 引擎（正确的配置方式）\n", "\n", "\n", "condense_question_plus_engine = index.as_chat_engine(\n", "    chat_mode=\"condense_plus_context\",\n", "    condense_prompt=new_condense_prompt,\n", "    context_prompt=custom_context_prompt,             # 配置上下文整合提示词\n", "    memory=memory,\n", "    # system_prompt=\"你是文文，一个热心活泼乐于助人的ai聊天助手，擅长查资料。你总是简洁、清晰、有条理地回应，使用很多的emoji。你回答总用“哈哈”开头。\",\n", "    verbose=True,\n", ")\n", "\n", "# 在condense_question_plus模式下，给llm的内容是context_prompt_template + system_prompt + chat_history + query_str,所以其实system_prompt没有必要\n", "\n", "# simple 引擎（不检索，只与LLM聊天，但同样共享 memory\n", "simple_engine = SimpleChatEngine.from_defaults(\n", "    llm=Settings.llm,\n", "    memory=memory,\n", "    system_prompt=\"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\",\n", "    verbose=True\n", ")\n", "print(\"✅ 两个引擎均已就绪，且共享同一份 ChatSummaryMemoryBuffer。\\n\")"]}, {"cell_type": "code", "execution_count": 181, "id": "a5ddae7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 探索 condense_plus_context 引擎的内部结构 ===\n", "可用属性: ['__abstractmethods__', '__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__slots__', '__str__', '__subclasshook__', '__weakref__', '_abc_impl', '_acondense_question', '_aget_nodes', '_arun_c3', '_condense_prompt_template', '_condense_question', '_context_prompt_template', '_context_refine_prompt_template', '_get_nodes', '_get_response_synthesizer', '_llm', '_memory', '_node_postprocessors', '_retriever', '_run_c3', '_skip_condense', '_system_prompt', '_token_counter', '_verbose', 'achat', 'astream_chat', 'callback_manager', 'chat', 'chat_history', 'chat_repl', 'from_defaults', 'reset', 'stream_chat', 'streaming_chat_repl']\n", "metadata={'prompt_type': <PromptType.CUSTOM: 'custom'>} template_vars=['chat_history', 'question'] kwargs={} output_parser=None template_var_mappings=None function_mappings=None template='你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\\n注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\\n=== 聊天历史 ===\\n{chat_history}\\n\\n=== 学生最新提出的问题 ===\\n{question}\\n\\n=== 改写后的独立问题 ===\\n'\n", "None\n", "metadata={'prompt_type': <PromptType.CUSTOM: 'custom'>} template_vars=['context_str', 'query_str'] kwargs={} output_parser=None template_var_mappings=None function_mappings=None template=\"你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\\n\\n📚 **相关文档内容：**\\n{context_str}\\n\\n🎯 **回答要求：**\\n1. 严格基于上述文档内容进行回答\\n2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\\n3. 回答要条理清晰，使用适当的emoji让内容更生动\\n4. 请引用具体的文档内容来支撑你的回答\\n\\n💡 **请基于以上文档和之前的对话历史来回答用户的问题。**根据以上信息，请回答这个问题: {query_str}\\n\\n====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\\n\\n\"\n", "metadata={'prompt_type': <PromptType.CUSTOM: 'custom'>} template_vars=['context_msg', 'existing_answer'] kwargs={} output_parser=None template_var_mappings=None function_mappings=None template=\"\\n  The following is a friendly conversation between a user and an AI assistant.\\n  The assistant is talkative and provides lots of specific details from its context.\\n  If the assistant does not know the answer to a question, it truthfully says it\\n  does not know.\\n\\n  Here are the relevant documents for the context:\\n\\n  {context_msg}\\n\\n  Existing Answer:\\n  {existing_answer}\\n\\n  Instruction: Refine the existing answer using the provided context to assist the user.\\n  If the context isn't helpful, just repeat the existing answer and nothing more.\\n  \"\n", "<bound method CondensePlusContextChatEngine._get_response_synthesizer of <llama_index.core.chat_engine.condense_plus_context.CondensePlusContextChatEngine object at 0x000001FC1412B2D0>>\n"]}], "source": ["# =============================\n", "# 正确获取和更新 condense_plus_context 提示词的方法\n", "\n", "\n", "# 2. 正确访问内部 query_engine（这是关键！）\n", "print(\"=== 探索 condense_plus_context 引擎的内部结构 ===\")\n", "print(\"可用属性:\", [attr for attr in dir(condense_question_plus_engine)])\n", "print(condense_question_plus_engine._condense_prompt_template)\n", "print(condense_question_plus_engine._system_prompt)\n", "print(condense_question_plus_engine._context_prompt_template)\n", "print(condense_question_plus_engine._context_refine_prompt_template)\n", "print(condense_question_plus_engine._get_response_synthesizer)\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 182, "id": "c3156159", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==========================================================================================\n", "🎯 欢迎使用交互式聊天终端！\n", "📋 可用命令：\n", "   1 - 切换到 condense_plus_context 模式（RAG检索）\n", "   2 - 切换到 simple 模式（纯对话）\n", "   /memory - 查看当前记忆状态\n", "   /reset - 重置对话记忆\n", "   /quit - 退出\n", "==========================================================================================\n", "\n", "\n", "🤖 condense_plus_context 回答：\n", "Condensed question: 今晚林志玲吃啥？\n", "文档中暂无相关信息，无法回答关于林志玲今晚吃什么的问题。不过，如果你对《红楼梦》中的人物或情节有任何疑问，我很乐意帮助你解答哦！😊📚\n", "\n", "------------------------------------------------------------\n", "\n", "🤖 condense_plus_context 回答：\n", "Condensed question: 林黛玉在《红楼梦》中是一个重要的角色，请问她的性格特点、背景故事以及与其他人物的关系是怎样的？\n", "林黛玉是《红楼梦》中的女主角之一，她的角色定位和性格特征如下：\n", "\n", "### 🌸 林黛玉的基本信息\n", "- **人物定位**：女主角，才情绝艳，寄人篱下的悲剧女性。\n", "- **性格特征**：\n", "  - 聪慧、敏感、孤傲、易感。\n", "  - 自幼失去母亲，身体羸弱，随父入京后寄居贾府，与宝玉青梅竹马。\n", "  - 心灵纯净，对爱情执着真诚，但因性格自卑、多疑，常陷入自怜与忧愁。\n", "\n", "### 📖 林黛玉的命运\n", "- 她与宝玉有“木石前盟”之约，但因家世、性格和环境种种阻隔，终以泪尽而逝。\n", "- 她的命运象征着理想爱情的幻灭，也映射出女性命运的无常。\n", "\n", "### 🌟 经典瞬间\n", "- 进贾府、葬花吟、焚稿断痴情、黛玉之死。\n", "\n", "林黛玉的故事充满了悲剧色彩，她的爱情与命运深刻地反映了《红楼梦》中的主题和情感。💔✨\n", "\n", "------------------------------------------------------------\n", "\n", "🤖 condense_plus_context 回答：\n", "Condensed question: 在学习Python函数时，学生们通常会遇到哪些具体的难点和挑战？这些难点可能包括函数的定义、参数传递、返回值的使用、作用域问题等方面吗？请详细说明。\n", "在学习Python函数时，可能会遇到以下几个难点：\n", "\n", "### 1. **理解函数的基本概念** 🤔\n", "   - 函数是代码的封装，能够接收输入（参数）并返回输出（返回值）。初学者可能会对如何定义和调用函数感到困惑。\n", "\n", "### 2. **参数的使用** 📦\n", "   - 学习如何使用位置实参和关键字实参，以及如何接受任意数量的实参。这些概念可能会让初学者感到复杂。\n", "\n", "### 3. **函数的返回值** 🔄\n", "   - 理解函数如何返回值，以及如何在程序中使用这些返回值，可能会让人感到不知所措。\n", "\n", "### 4. **函数与其他结构的结合** 🔗\n", "   - 如何将函数与列表、字典、if 语句和while循环结合使用，这需要一定的逻辑思维能力。\n", "\n", "### 5. **模块化编程** 📂\n", "   - 学习如何将函数存储在模块中，以使程序更简单、更易于理解。初学者可能会对模块的概念感到陌生。\n", "\n", "### 6. **遵循编写指南** 📏\n", "   - 理解并遵循函数编写的最佳实践和指南，以保持代码的良好结构和可读性。\n", "\n", "### 小结\n", "正如文档中提到的，程序员的目标之一是编写简单的代码来完成任务，而函数有助于实现这一目标。通过不断练习和应用这些概念，学习者可以逐渐克服这些难点，提升编程能力！💪✨\n", "\n", "希望这些信息能帮助你更好地理解Python函数的学习过程！如果还有其他问题，随时问我哦！😊\n", "\n", "------------------------------------------------------------\n", "\n", "🤖 condense_plus_context 回答：\n", "Condensed question: 在学习Python编程时，像《红楼梦》中的人物林黛玉那样敏感和复杂的角色，可能会在理解和应用函数时遇到哪些具体的难点？例如，如何掌握函数的定义、参数的使用、返回值的理解，以及如何将函数与其他编程结构结合使用等方面的挑战？\n", "在《红楼梦》中，人物学习Python可能会遇到以下几个难点，结合他们的性格和背景来分析：\n", "\n", "### 1. **理解抽象概念** 🤔\n", "   - **人物背景**：林黛玉聪慧但敏感，可能对抽象的编程概念（如函数、参数等）感到困惑。\n", "   - **难点**：函数的定义和调用、参数的传递等基础概念可能让她感到难以理解。\n", "\n", "### 2. **逻辑思维能力** 🧠\n", "   - **人物背景**：贾宝玉性情洒脱，可能不太擅长严谨的逻辑思维。\n", "   - **难点**：编程需要严密的逻辑思维，宝玉可能在编写条件语句和循环时遇到困难。\n", "\n", "### 3. **调试与错误处理** 🔍\n", "   - **人物背景**：王熙凤精明强干，但对细节的忽视可能导致错误。\n", "   - **难点**：调试代码时，如何找到并修复错误可能会让她感到挫败。\n", "\n", "### 4. **函数的复用与模块化** 📦\n", "   - **人物背景**：众多角色之间的关系复杂，可能对模块化编程的理解不够深入。\n", "   - **难点**：如何将函数存储在模块中以提高代码的可读性和复用性，可能会让他们感到困惑。\n", "\n", "### 5. **团队协作与代码共享** 🤝\n", "   - **人物背景**：贾府中的人物关系复杂，合作时可能存在意见不合。\n", "   - **难点**：在团队中共享代码和协作编程时，如何保持代码的整洁和一致性可能会成为挑战。\n", "\n", "### 小结\n", "虽然《红楼梦》中的人物在学习Python时可能会遇到这些难点，但通过不断的练习和相互帮助，他们也能逐渐克服这些困难，掌握编程的技巧！💪✨\n", "\n", "如果你还有其他问题或想了解更多，随时告诉我哦！😊\n", "\n", "------------------------------------------------------------\n", "\n", "🤖 condense_plus_context 回答：\n", "Condensed question: 在《红楼梦》中，林黛玉、贾宝玉和王熙凤等人物如果学习Python编程，他们可能会遇到哪些具体的难点？请结合他们的性格特点和背景，详细分析这些难点，并给出可能的解决方案。\n", "### 📚 《红楼梦中的人物与Python学习的奇妙旅程》 🌟\n", "\n", "在古典名著《红楼梦》中，众多鲜活的人物如林黛玉、贾宝玉和王熙凤等，生活在一个充满情感与复杂关系的世界里。假如他们穿越时空，来到现代，开始学习Python编程，这将是多么有趣的场景啊！🤖✨\n", "\n", "#### 🌸 林黛玉的编程挑战\n", "\n", "林黛玉，聪慧而敏感，面对Python的抽象概念时，可能会感到困惑。她在学习函数的定义和调用时，常常皱着眉头，思考着：“这到底是个什么鬼？”虽然她的才情出众，但编程的逻辑思维却让她感到无从下手。😅\n", "\n", "#### 🌟 贾宝玉的洒脱与逻辑\n", "\n", "贾宝玉性情洒脱，热爱自由，但在编写条件语句和循环时，他可能会陷入思维的迷雾中。他常常一边编程，一边感叹：“这代码就像我的爱情，复杂而难以捉摸！”虽然他对编程的热情高涨，但严谨的逻辑思维却让他感到无奈。🤔💔\n", "\n", "#### 🔍 王熙凤的精明与调试\n", "\n", "王熙凤，精明强干，面对调试与错误处理时，她的细致与果断让她在编程中游刃有余。然而，偶尔的疏忽也会让她在调试时感到挫败。她会对着屏幕自言自语：“这代码怎么又出错了？难道是我太忙了？”😤\n", "\n", "#### 📦 模块化的团队协作\n", "\n", "在贾府这个大家庭中，人物关系复杂，合作时常常会出现意见不合。在学习模块化编程时，他们需要学会如何将函数存储在模块中，以提高代码的可读性和复用性。贾母可能会说：“我们要像处理家务一样，合理分工，才能让代码更整洁！”🤝\n", "\n", "### 🌈 小结\n", "\n", "虽然《红楼梦》中的人物在学习Python时会遇到许多挑战，但通过不断的练习和相互帮助，他们一定能够克服这些困难，掌握编程的技巧！这不仅是一次编程的旅程，更是一次情感与智慧的碰撞。💪✨\n", "\n", "在这个充满挑战与乐趣的学习过程中，他们将发现，编程不仅仅是代码的堆砌，更是创造与表达的艺术！🎨💻\n", "\n", "---\n", "\n", "希望这篇作文能让你感受到《红楼梦》人物与Python学习的奇妙结合！如果你还有其他问题或想法，随时告诉我哦！😊\n", "\n", "------------------------------------------------------------\n", "👋 再见！\n"]}], "source": ["# =============================\n", "#  交互式对话终端 多聊几轮 看见 role': 'system' 就是压缩后的信息\n", "# 这个inspect_memory_messages就没有定义呀\n", "# =============================\n", "def interactive_chat():\n", "    \"\"\"交互式聊天终端，支持切换对话模式\"\"\"\n", "    \n", "    print(\"\\n\" + \"=\"*90)\n", "    print(\"🎯 欢迎使用交互式聊天终端！\")\n", "    print(\"📋 可用命令：\")\n", "    print(\"   1 - 切换到 condense_plus_context 模式（RAG检索）\")  # 更新模式名称\n", "    print(\"   2 - 切换到 simple 模式（纯对话）\")\n", "    print(\"   /memory - 查看当前记忆状态\")\n", "    print(\"   /reset - 重置对话记忆\")\n", "    print(\"   /quit - 退出\")\n", "    print(\"=\"*90 + \"\\n\")\n", "    \n", "    current_engine = condense_question_plus_engine  # 修改：直接引用变量\n", "    current_mode = \"condense_plus_context\"  # 修改：更新模式名称\n", "    \n", "    while True:\n", "        # 显示当前模式\n", "        mode_display = \"🔎 RAG检索\" if current_mode == \"condense_plus_context\" else \"💬 纯对话\"\n", "        user_input = input(f\"[{mode_display}] 👤 你: \").strip()\n", "        \n", "        if not user_input:\n", "            continue\n", "            \n", "        # 处理命令\n", "        if user_input == \"1\":\n", "            current_engine = condense_question_plus_engine  # 修改：直接引用变量\n", "            current_mode = \"condense_plus_context\"  # 修改：更新模式名称\n", "            print(\"✅ 已切换到 condense_plus_context 模式（RAG检索）\\n\")\n", "            continue\n", "        elif user_input == \"2\":\n", "            current_engine = simple_engine\n", "            current_mode = \"simple\"\n", "            print(\"✅ 已切换到 simple 模式（纯对话）\\n\")\n", "            continue\n", "        elif user_input == \"/reset\":\n", "            memory.reset()\n", "            print(\"🔄 记忆已重置\\n\")\n", "            continue\n", "        elif user_input == \"/quit\":\n", "            print(\"👋 再见！\")\n", "            break\n", "            \n", "        # 处理用户问题\n", "        try:\n", "            print(f\"\\n🤖 {current_mode} 回答：\")\n", "            response = current_engine.chat(user_input)\n", "            print(f\"{response}\\n\")\n", "            print(\"-\" * 60)\n", "        except Exception as e:\n", "            print(f\"❌ 出错了: {e}\\n\")\n", "\n", "# 启动交互式聊天\n", "interactive_chat()"]}, {"cell_type": "code", "execution_count": 167, "id": "8c7f2ae8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== condense_plus_chat 完整内容 ===\n", "[0] {'role': 'system', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '在这段对话中，学生的核心问题集中在如何有效应对头痛，寻求缓解方法以减轻不适感。学生希望找到切实可行的解决方案来处理这一困扰。\\n\\n助理提供了多种应对头痛的建议，包括：\\n1. **休息**：在安静昏暗的环境中放松。\\n2. **喝水**：补充水分以防脱水。\\n3. **冷敷或热敷**：根据头痛类型选择适合的敷法。\\n4. **按摩**：通过按摩缓解紧张。\\n5. **深呼吸**：进行深呼吸练习以放松身心。\\n6. **药物**：在必要时使用非处方药物，但需遵循医生建议。\\n7. **避免刺激**：远离噪音和强光等刺激源。\\n\\n助理强调，如果头痛持续或加重，建议及时就医，以确保健康安全。整体上，助理的建议旨在帮助学生找到适合自己的缓解方法，并鼓励学生关注自身健康。'}]}\n", "[1] {'role': 'user', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '你老老实实告诉我，我之前问过你哪些问题？'}]}\n", "[2] {'role': 'assistant', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '哈哈，我们之前聊过以下几个问题哦！😊✨\\n\\n1. **Python函数**：你询问了关于Python函数的定义和用途，我解释了如何使用函数来处理列表，并给出了`greet_users()`的示例。\\n\\n2. **林黛玉**：你问了林黛玉的身份，我提供了她在《红楼梦》中的背景信息和性格特征。\\n\\n3. **话题相关性**：我们讨论了Python函数和林黛玉之间的联系，强调了它们在各自领域中的重要性。\\n\\n4. **健康话题**：你问了关于健康的话题，我提到我们没有讨论过这个内容。\\n\\n如果你还有其他问题或者想要讨论的内容，随时告诉我哦！我很乐意继续帮助你！🌟📖'}]}\n", "[3] {'role': 'user', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '我们没有讨论过头痛？'}]}\n", "[4] {'role': 'assistant', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '哈哈，我们确实没有讨论过头痛哦！😅 如果你对头痛有任何疑问或者想要了解缓解方法，随时可以问我！我很乐意提供相关的信息和建议！💊✨'}]}\n", "[5] {'role': 'user', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '假如一个人不开心，应该怎么办？'}]}\n", "[6] {'role': 'assistant', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '哈哈，如果一个人不开心，可以尝试以下几种方法来调整心情哦！😊✨\\n\\n1. **倾诉**：找朋友或家人聊聊，把心里的烦恼说出来，分享感受可以减轻压力。\\n\\n2. **休息放松**：给自己一些时间，做一些喜欢的事情，比如看电影、听音乐、阅读等，帮助自己放松心情。\\n\\n3. **运动**：适当的运动可以释放内啡肽，提升情绪，比如散步、跑步或做瑜伽。\\n\\n4. **写日记**：把自己的感受写下来，可以帮助理清思绪，找到问题的根源。\\n\\n5. **寻求专业帮助**：如果情绪持续低落，可以考虑咨询心理医生或专业人士，获得更专业的建议。\\n\\n6. **关注自我**：保持健康的生活方式，均衡饮食、充足睡眠和适量运动，都是改善情绪的重要因素。\\n\\n希望这些建议能帮助到你或你关心的人！如果还有其他问题，随时告诉我哦！🌈💖'}]}\n", "[7] {'role': 'user', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '我们刚才都讨论了啥？'}]}\n", "[8] {'role': 'assistant', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '哈哈，我们刚才讨论了关于如何应对不开心的情绪哦！😊✨ 我提供了一些建议，比如倾诉、休息放松、运动、写日记、寻求专业帮助和关注自我等方法，帮助改善情绪。\\n\\n如果你还有其他问题或者想要深入讨论的内容，随时告诉我哦！我很乐意继续帮助你！🌟💬'}]}\n", "[9] {'role': 'user', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '我们之前聊到了红楼梦的哪些角色？'}]}\n", "[10] {'role': 'assistant', 'additional_kwargs': {}, 'blocks': [{'block_type': 'text', 'text': '哈哈，我们之前讨论了《红楼梦》中的一些角色哦！😊✨\\n\\n在文档中提到的角色包括：\\n\\n- **尤三姐**：她是一个重要的配角，具有很高的文学价值。\\n- **晴雯**：同样是一个重要的配角，值得关注。\\n- **小红**：也是书中的配角之一。\\n\\n此外，我们还提到了一些关于人物混淆的常见误区，建议使用人物表来对照，避免混淆同名或同姓的角色。\\n\\n如果你想了解更多角色或其他相关内容，随时告诉我哦！📖💖'}]}\n"]}], "source": ["# 直接输出redis_llama_chat_001键的完整值\n", "import redis\n", "import json\n", "\n", "redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)\n", "values = redis_client.lrange(\"condense_plus_chat\", 0, -1)\n", "\n", "print(\"=== condense_plus_chat 完整内容 ===\")\n", "for i, value in enumerate(values):\n", "    parsed = json.loads(value)\n", "    print(f\"[{i}] {parsed}\")"]}, {"cell_type": "code", "execution_count": 58, "id": "7674ec19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Memory 完整信息 ===\n", "Memory类型: <class 'llama_index.core.memory.chat_summary_memory_buffer.ChatSummaryMemoryBuffer'>\n", "Token限制: 1000\n", "Chat Store: redis_url='redis://localhost:6379' ttl=3000\n", "Chat Store Key: redis_llama_shortchat_001\n", "Tokenizer函数: functools.partial(<bound method Encoding.encode of <Encoding 'cl100k_base'>>, allowed_special='all')\n", "摘要提示词: metadata={'prompt_type': <PromptType.CUSTOM: 'custom'>} template_vars=[] kwargs={} output_parser=None template_var_mappings=None function_mappings=None template='你是对话记忆助理。请在 300 字内用总结对话要点，突出：\\n1) 学生的核心问题和困惑点；\\n2) 已经给出的关键信息、结论和思路。\\n'\n", "\n", "=== Memory.get() 消息列表（传给LLM的最终消息）===\n", "[0] Role: MessageRole.SYSTEM\n", "    Content: ### 婚姻选择标准函数设计\n", "\n", "在这个设计中，我们将创建一个函数来评估候选女性是否符合贾宝玉的娶妻标准。我们将考虑多个因素，包括性格特征、情感共鸣、家庭背景、关怀程度和价值观。以下是函数的详细实现：\n", "\n", "```python\n", "class Woman:\n", "    def __init__(self, name, personality, emotional_resonance, family_background, care_level, values):\n", "        self.name = name  # 女性的名字\n", "        self.personality = personality  # 性格特征\n", "        self.emotional_resonance = emotional_resonance  # 情感共鸣评分（1-10）\n", "        self.family_background = family_background  # 家庭背景\n", "        self.care_level = care_level  # 关怀程度评分（1-10）\n", "        self.values = values  # 价值观\n", "\n", "def marriage_selection_criteria(woman, jia_baoyu):\n", "    score = 0\n", "    \n", "    # 性格特征匹配\n", "    if woman.personality in ['叛逆', '敏感', '真挚']:\n", "        score += 2\n", "    \n", "    # 情感共鸣\n", "    if woman.emotional_resonance >= jia_baoyu.emotional_needs:\n", "        score += 3\n", "    \n", "    # 家庭背景\n", "    if woman.family_background in ['王家', '薛家']:\n", "        score += 1\n", "    \n", "    # 对女性的关怀\n", "    if woman.care_level >= jia_baoyu.care_standard:\n", "        score += 2\n", "    \n", "    # 反对封建礼教\n", "    if woman.values == '反对封建礼教':\n", "        score += 2\n", "    \n", "    return score\n", "\n", "# 贾宝玉的标准\n", "class JiaBaoyu:\n", "    def __init__(self):\n", "        self.emotional_needs = 8  # 情感需求评分\n", "        self.care_standard = 7  # 关怀标准评分\n", "\n", "# 示例候选女性\n", "women = [\n", "    Woman(\"林黛玉\", \"叛逆\", 9, \"王家\", 8, \"反对封建礼教\"),\n", "    Woman(\"薛宝钗\", \"温柔\", 8, \"薛家\", 7, \"支持封建礼教\"),\n", "    Woman(\"王熙凤\", \"强势\", 6, \"王家\", 5, \"反对封建礼教\"),\n", "    Woman(\"贾探春\", \"真挚\", 7, \"贾家\", 9, \"反对封建礼教\")\n", "]\n", "\n", "# 创建贾宝玉实例\n", "jia_baoyu = JiaBaoyu()\n", "\n", "# 评估候选女性\n", "for woman in women:\n", "    score = marriage_selection_criteria(woman, jia_baoyu)\n", "    if score >= 7:\n", "        print(f\"{woman.name} 符合贾宝玉的娶妻标准，得分: {score}\")\n", "    else:\n", "        print(f\"{woman.name} 不符合贾宝玉的娶妻标准，得分: {score}\")\n", "```\n", "\n", "### 解释\n", "\n", "1. **Woman类**：定义了女性的属性，包括名字、性格特征、情感共鸣、家庭背景、关怀程度和价值观。\n", "\n", "2. **marriage_selection_criteria函数**：根据贾宝玉的标准评估每位女性的得分。得分越高，表示越符合贾宝玉的标准。\n", "\n", "3. **JiaBaoyu类**：定义了贾宝玉的情感需求和关怀标准。\n", "\n", "4. **示例候选女性**：创建了一些女性实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "5. **评估结果**：遍历候选女性，计算得分并输出符合或不符合贾宝玉标准的结果。\n", "\n", "### 总结\n", "\n", "通过这种方式，贾宝玉能够系统地评估每位候选女性，确保选择符合他内心追求的伴侣。这种设计不仅考虑了情感和性格，还兼顾了家庭背景和价值观，使得选择更加全面和合理。\n", "    完整对象: system: ### 婚姻选择标准函数设计\n", "\n", "在这个设计中，我们将创建一个函数来评估候选女性是否符合贾宝玉的娶妻标准。我们将考虑多个因素，包括性格特征、情感共鸣、家庭背景、关怀程度和价值观。以下是函数的详细实现：\n", "\n", "```python\n", "class Woman:\n", "    def __init__(self, name, personality, emotional_resonance, family_background, care_level, values):\n", "        self.name = name  # 女性的名字\n", "        self.personality = personality  # 性格特征\n", "        self.emotional_resonance = emotional_resonance  # 情感共鸣评分（1-10）\n", "        self.family_background = family_background  # 家庭背景\n", "        self.care_level = care_level  # 关怀程度评分（1-10）\n", "        self.values = values  # 价值观\n", "\n", "def marriage_selection_criteria(woman, jia_baoyu):\n", "    score = 0\n", "    \n", "    # 性格特征匹配\n", "    if woman.personality in ['叛逆', '敏感', '真挚']:\n", "        score += 2\n", "    \n", "    # 情感共鸣\n", "    if woman.emotional_resonance >= jia_baoyu.emotional_needs:\n", "        score += 3\n", "    \n", "    # 家庭背景\n", "    if woman.family_background in ['王家', '薛家']:\n", "        score += 1\n", "    \n", "    # 对女性的关怀\n", "    if woman.care_level >= jia_baoyu.care_standard:\n", "        score += 2\n", "    \n", "    # 反对封建礼教\n", "    if woman.values == '反对封建礼教':\n", "        score += 2\n", "    \n", "    return score\n", "\n", "# 贾宝玉的标准\n", "class JiaBaoyu:\n", "    def __init__(self):\n", "        self.emotional_needs = 8  # 情感需求评分\n", "        self.care_standard = 7  # 关怀标准评分\n", "\n", "# 示例候选女性\n", "women = [\n", "    Woman(\"林黛玉\", \"叛逆\", 9, \"王家\", 8, \"反对封建礼教\"),\n", "    Woman(\"薛宝钗\", \"温柔\", 8, \"薛家\", 7, \"支持封建礼教\"),\n", "    Woman(\"王熙凤\", \"强势\", 6, \"王家\", 5, \"反对封建礼教\"),\n", "    Woman(\"贾探春\", \"真挚\", 7, \"贾家\", 9, \"反对封建礼教\")\n", "]\n", "\n", "# 创建贾宝玉实例\n", "jia_baoyu = JiaBaoyu()\n", "\n", "# 评估候选女性\n", "for woman in women:\n", "    score = marriage_selection_criteria(woman, jia_baoyu)\n", "    if score >= 7:\n", "        print(f\"{woman.name} 符合贾宝玉的娶妻标准，得分: {score}\")\n", "    else:\n", "        print(f\"{woman.name} 不符合贾宝玉的娶妻标准，得分: {score}\")\n", "```\n", "\n", "### 解释\n", "\n", "1. **Woman类**：定义了女性的属性，包括名字、性格特征、情感共鸣、家庭背景、关怀程度和价值观。\n", "\n", "2. **marriage_selection_criteria函数**：根据贾宝玉的标准评估每位女性的得分。得分越高，表示越符合贾宝玉的标准。\n", "\n", "3. **JiaBaoyu类**：定义了贾宝玉的情感需求和关怀标准。\n", "\n", "4. **示例候选女性**：创建了一些女性实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "5. **评估结果**：遍历候选女性，计算得分并输出符合或不符合贾宝玉标准的结果。\n", "\n", "### 总结\n", "\n", "通过这种方式，贾宝玉能够系统地评估每位候选女性，确保选择符合他内心追求的伴侣。这种设计不仅考虑了情感和性格，还兼顾了家庭背景和价值观，使得选择更加全面和合理。\n", "------------------------------------------------------------\n", "[1] Role: MessageRole.USER\n", "    Content: 总结我们刚才聊的内容\n", "    完整对象: user: 总结我们刚才聊的内容\n", "------------------------------------------------------------\n", "[2] Role: MessageRole.ASSISTANT\n", "    Content: 当然可以！😊\n", "\n", "### 总结内容\n", "\n", "1. **函数设计**：\n", "   - 创建了一个 `Candidate` 类，包含候选人的属性，如真诚度、情感连接、家庭背景、对关怀的欣赏和是否拥护封建礼教。\n", "   - 实现了 `choose_wife` 函数，遍历候选人，筛选出符合贾宝玉标准的配偶。\n", "\n", "2. **筛选标准**：\n", "   - 候选人必须真诚。\n", "   - 情感连接评分需达到8分及以上。\n", "   - 家庭背景需为王家或薛家。\n", "   - 候选人需欣赏贾宝玉的关怀。\n", "   - 不拥护封建礼教。\n", "\n", "3. **示例候选人**：\n", "   - 提供了几个候选人实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "4. **输出结果**：\n", "   - 打印出符合条件的配偶名字。\n", "\n", "5. **扩展思路**：\n", "   - 提出了一个婚姻选择标准的伪代码，考虑性格特征、情感共鸣、家庭背景、关怀程度和价值观等因素，以帮助贾宝玉做出更合适的选择。\n", "\n", "希望这个总结对你有帮助！如果还有其他问题，随时问我哦！🌟\n", "    完整对象: assistant: 当然可以！😊\n", "\n", "### 总结内容\n", "\n", "1. **函数设计**：\n", "   - 创建了一个 `Candidate` 类，包含候选人的属性，如真诚度、情感连接、家庭背景、对关怀的欣赏和是否拥护封建礼教。\n", "   - 实现了 `choose_wife` 函数，遍历候选人，筛选出符合贾宝玉标准的配偶。\n", "\n", "2. **筛选标准**：\n", "   - 候选人必须真诚。\n", "   - 情感连接评分需达到8分及以上。\n", "   - 家庭背景需为王家或薛家。\n", "   - 候选人需欣赏贾宝玉的关怀。\n", "   - 不拥护封建礼教。\n", "\n", "3. **示例候选人**：\n", "   - 提供了几个候选人实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "4. **输出结果**：\n", "   - 打印出符合条件的配偶名字。\n", "\n", "5. **扩展思路**：\n", "   - 提出了一个婚姻选择标准的伪代码，考虑性格特征、情感共鸣、家庭背景、关怀程度和价值观等因素，以帮助贾宝玉做出更合适的选择。\n", "\n", "希望这个总结对你有帮助！如果还有其他问题，随时问我哦！🌟\n", "------------------------------------------------------------\n", "[3] Role: MessageRole.USER\n", "    Content: 通过刚才我问你的问题，有大五人格思考我大概是什么人格\n", "    完整对象: user: 通过刚才我问你的问题，有大五人格思考我大概是什么人格\n", "------------------------------------------------------------\n", "[4] Role: MessageRole.ASSISTANT\n", "    Content: 根据大五人格理论（OCEAN），我们可以从以下五个维度来分析你的性格特征：\n", "\n", "1. **开放性（Openness）**：你对新体验和创意的接受程度。你提问的内容显示出你对复杂问题的兴趣，可能在这一维度上得分较高。\n", "\n", "2. **责任心（Conscientiousness）**：你在组织和计划方面的能力。你对婚姻选择标准的关注表明你可能是一个考虑周到、注重细节的人。\n", "\n", "3. **外向性（Extraversion）**：你与他人互动的倾向。你的提问方式友好且开放，可能表明你在这一维度上得分中等或偏高。\n", "\n", "4. **宜人性（Agreeableness）**：你对他人感受的敏感程度。你对婚姻选择的关注显示出你可能比较关心他人的情感和需求。\n", "\n", "5. **神经质（Neuroticism）**：你情绪稳定性的程度。这个维度需要更多信息来判断，但如果你对复杂情感问题感兴趣，可能表明你在这一维度上得分较低。\n", "\n", "### 总结\n", "综合来看，你可能是一个开放、责任心强、友好且关心他人的人。😊 如果你想更深入了解自己的性格，可以考虑进行正式的人格测试哦！🌈\n", "    完整对象: assistant: 根据大五人格理论（OCEAN），我们可以从以下五个维度来分析你的性格特征：\n", "\n", "1. **开放性（Openness）**：你对新体验和创意的接受程度。你提问的内容显示出你对复杂问题的兴趣，可能在这一维度上得分较高。\n", "\n", "2. **责任心（Conscientiousness）**：你在组织和计划方面的能力。你对婚姻选择标准的关注表明你可能是一个考虑周到、注重细节的人。\n", "\n", "3. **外向性（Extraversion）**：你与他人互动的倾向。你的提问方式友好且开放，可能表明你在这一维度上得分中等或偏高。\n", "\n", "4. **宜人性（Agreeableness）**：你对他人感受的敏感程度。你对婚姻选择的关注显示出你可能比较关心他人的情感和需求。\n", "\n", "5. **神经质（Neuroticism）**：你情绪稳定性的程度。这个维度需要更多信息来判断，但如果你对复杂情感问题感兴趣，可能表明你在这一维度上得分较低。\n", "\n", "### 总结\n", "综合来看，你可能是一个开放、责任心强、友好且关心他人的人。😊 如果你想更深入了解自己的性格，可以考虑进行正式的人格测试哦！🌈\n", "------------------------------------------------------------\n", "\n", "=== Memory.get_all() 所有原始消息 ===\n", "[0] Role: MessageRole.SYSTEM\n", "    Content: ### 婚姻选择标准函数设计\n", "\n", "在这个设计中，我们将创建一个函数来评估候选女性是否符合贾宝玉的娶妻标准。我们将考虑多个因素，包括性格特征、情感共鸣、家庭背景、关怀程度和价值观。以下是函数的详细实现：\n", "\n", "```python\n", "class Woman:\n", "    def __init__(self, name, personality, emotional_resonance, family_background, care_level, values):\n", "        self.name = name  # 女性的名字\n", "        self.personality = personality  # 性格特征\n", "        self.emotional_resonance = emotional_resonance  # 情感共鸣评分（1-10）\n", "        self.family_background = family_background  # 家庭背景\n", "        self.care_level = care_level  # 关怀程度评分（1-10）\n", "        self.values = values  # 价值观\n", "\n", "def marriage_selection_criteria(woman, jia_baoyu):\n", "    score = 0\n", "    \n", "    # 性格特征匹配\n", "    if woman.personality in ['叛逆', '敏感', '真挚']:\n", "        score += 2\n", "    \n", "    # 情感共鸣\n", "    if woman.emotional_resonance >= jia_baoyu.emotional_needs:\n", "        score += 3\n", "    \n", "    # 家庭背景\n", "    if woman.family_background in ['王家', '薛家']:\n", "        score += 1\n", "    \n", "    # 对女性的关怀\n", "    if woman.care_level >= jia_baoyu.care_standard:\n", "        score += 2\n", "    \n", "    # 反对封建礼教\n", "    if woman.values == '反对封建礼教':\n", "        score += 2\n", "    \n", "    return score\n", "\n", "# 贾宝玉的标准\n", "class JiaBaoyu:\n", "    def __init__(self):\n", "        self.emotional_needs = 8  # 情感需求评分\n", "        self.care_standard = 7  # 关怀标准评分\n", "\n", "# 示例候选女性\n", "women = [\n", "    Woman(\"林黛玉\", \"叛逆\", 9, \"王家\", 8, \"反对封建礼教\"),\n", "    Woman(\"薛宝钗\", \"温柔\", 8, \"薛家\", 7, \"支持封建礼教\"),\n", "    Woman(\"王熙凤\", \"强势\", 6, \"王家\", 5, \"反对封建礼教\"),\n", "    Woman(\"贾探春\", \"真挚\", 7, \"贾家\", 9, \"反对封建礼教\")\n", "]\n", "\n", "# 创建贾宝玉实例\n", "jia_baoyu = JiaBaoyu()\n", "\n", "# 评估候选女性\n", "for woman in women:\n", "    score = marriage_selection_criteria(woman, jia_baoyu)\n", "    if score >= 7:\n", "        print(f\"{woman.name} 符合贾宝玉的娶妻标准，得分: {score}\")\n", "    else:\n", "        print(f\"{woman.name} 不符合贾宝玉的娶妻标准，得分: {score}\")\n", "```\n", "\n", "### 解释\n", "\n", "1. **Woman类**：定义了女性的属性，包括名字、性格特征、情感共鸣、家庭背景、关怀程度和价值观。\n", "\n", "2. **marriage_selection_criteria函数**：根据贾宝玉的标准评估每位女性的得分。得分越高，表示越符合贾宝玉的标准。\n", "\n", "3. **JiaBaoyu类**：定义了贾宝玉的情感需求和关怀标准。\n", "\n", "4. **示例候选女性**：创建了一些女性实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "5. **评估结果**：遍历候选女性，计算得分并输出符合或不符合贾宝玉标准的结果。\n", "\n", "### 总结\n", "\n", "通过这种方式，贾宝玉能够系统地评估每位候选女性，确保选择符合他内心追求的伴侣。这种设计不仅考虑了情感和性格，还兼顾了家庭背景和价值观，使得选择更加全面和合理。\n", "    完整对象: system: ### 婚姻选择标准函数设计\n", "\n", "在这个设计中，我们将创建一个函数来评估候选女性是否符合贾宝玉的娶妻标准。我们将考虑多个因素，包括性格特征、情感共鸣、家庭背景、关怀程度和价值观。以下是函数的详细实现：\n", "\n", "```python\n", "class Woman:\n", "    def __init__(self, name, personality, emotional_resonance, family_background, care_level, values):\n", "        self.name = name  # 女性的名字\n", "        self.personality = personality  # 性格特征\n", "        self.emotional_resonance = emotional_resonance  # 情感共鸣评分（1-10）\n", "        self.family_background = family_background  # 家庭背景\n", "        self.care_level = care_level  # 关怀程度评分（1-10）\n", "        self.values = values  # 价值观\n", "\n", "def marriage_selection_criteria(woman, jia_baoyu):\n", "    score = 0\n", "    \n", "    # 性格特征匹配\n", "    if woman.personality in ['叛逆', '敏感', '真挚']:\n", "        score += 2\n", "    \n", "    # 情感共鸣\n", "    if woman.emotional_resonance >= jia_baoyu.emotional_needs:\n", "        score += 3\n", "    \n", "    # 家庭背景\n", "    if woman.family_background in ['王家', '薛家']:\n", "        score += 1\n", "    \n", "    # 对女性的关怀\n", "    if woman.care_level >= jia_baoyu.care_standard:\n", "        score += 2\n", "    \n", "    # 反对封建礼教\n", "    if woman.values == '反对封建礼教':\n", "        score += 2\n", "    \n", "    return score\n", "\n", "# 贾宝玉的标准\n", "class JiaBaoyu:\n", "    def __init__(self):\n", "        self.emotional_needs = 8  # 情感需求评分\n", "        self.care_standard = 7  # 关怀标准评分\n", "\n", "# 示例候选女性\n", "women = [\n", "    Woman(\"林黛玉\", \"叛逆\", 9, \"王家\", 8, \"反对封建礼教\"),\n", "    Woman(\"薛宝钗\", \"温柔\", 8, \"薛家\", 7, \"支持封建礼教\"),\n", "    Woman(\"王熙凤\", \"强势\", 6, \"王家\", 5, \"反对封建礼教\"),\n", "    Woman(\"贾探春\", \"真挚\", 7, \"贾家\", 9, \"反对封建礼教\")\n", "]\n", "\n", "# 创建贾宝玉实例\n", "jia_baoyu = JiaBaoyu()\n", "\n", "# 评估候选女性\n", "for woman in women:\n", "    score = marriage_selection_criteria(woman, jia_baoyu)\n", "    if score >= 7:\n", "        print(f\"{woman.name} 符合贾宝玉的娶妻标准，得分: {score}\")\n", "    else:\n", "        print(f\"{woman.name} 不符合贾宝玉的娶妻标准，得分: {score}\")\n", "```\n", "\n", "### 解释\n", "\n", "1. **Woman类**：定义了女性的属性，包括名字、性格特征、情感共鸣、家庭背景、关怀程度和价值观。\n", "\n", "2. **marriage_selection_criteria函数**：根据贾宝玉的标准评估每位女性的得分。得分越高，表示越符合贾宝玉的标准。\n", "\n", "3. **JiaBaoyu类**：定义了贾宝玉的情感需求和关怀标准。\n", "\n", "4. **示例候选女性**：创建了一些女性实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "5. **评估结果**：遍历候选女性，计算得分并输出符合或不符合贾宝玉标准的结果。\n", "\n", "### 总结\n", "\n", "通过这种方式，贾宝玉能够系统地评估每位候选女性，确保选择符合他内心追求的伴侣。这种设计不仅考虑了情感和性格，还兼顾了家庭背景和价值观，使得选择更加全面和合理。\n", "------------------------------------------------------------\n", "[1] Role: MessageRole.USER\n", "    Content: 总结我们刚才聊的内容\n", "    完整对象: user: 总结我们刚才聊的内容\n", "------------------------------------------------------------\n", "[2] Role: MessageRole.ASSISTANT\n", "    Content: 当然可以！😊\n", "\n", "### 总结内容\n", "\n", "1. **函数设计**：\n", "   - 创建了一个 `Candidate` 类，包含候选人的属性，如真诚度、情感连接、家庭背景、对关怀的欣赏和是否拥护封建礼教。\n", "   - 实现了 `choose_wife` 函数，遍历候选人，筛选出符合贾宝玉标准的配偶。\n", "\n", "2. **筛选标准**：\n", "   - 候选人必须真诚。\n", "   - 情感连接评分需达到8分及以上。\n", "   - 家庭背景需为王家或薛家。\n", "   - 候选人需欣赏贾宝玉的关怀。\n", "   - 不拥护封建礼教。\n", "\n", "3. **示例候选人**：\n", "   - 提供了几个候选人实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "4. **输出结果**：\n", "   - 打印出符合条件的配偶名字。\n", "\n", "5. **扩展思路**：\n", "   - 提出了一个婚姻选择标准的伪代码，考虑性格特征、情感共鸣、家庭背景、关怀程度和价值观等因素，以帮助贾宝玉做出更合适的选择。\n", "\n", "希望这个总结对你有帮助！如果还有其他问题，随时问我哦！🌟\n", "    完整对象: assistant: 当然可以！😊\n", "\n", "### 总结内容\n", "\n", "1. **函数设计**：\n", "   - 创建了一个 `Candidate` 类，包含候选人的属性，如真诚度、情感连接、家庭背景、对关怀的欣赏和是否拥护封建礼教。\n", "   - 实现了 `choose_wife` 函数，遍历候选人，筛选出符合贾宝玉标准的配偶。\n", "\n", "2. **筛选标准**：\n", "   - 候选人必须真诚。\n", "   - 情感连接评分需达到8分及以上。\n", "   - 家庭背景需为王家或薛家。\n", "   - 候选人需欣赏贾宝玉的关怀。\n", "   - 不拥护封建礼教。\n", "\n", "3. **示例候选人**：\n", "   - 提供了几个候选人实例，模拟贾宝玉可能考虑的配偶。\n", "\n", "4. **输出结果**：\n", "   - 打印出符合条件的配偶名字。\n", "\n", "5. **扩展思路**：\n", "   - 提出了一个婚姻选择标准的伪代码，考虑性格特征、情感共鸣、家庭背景、关怀程度和价值观等因素，以帮助贾宝玉做出更合适的选择。\n", "\n", "希望这个总结对你有帮助！如果还有其他问题，随时问我哦！🌟\n", "------------------------------------------------------------\n", "[3] Role: MessageRole.USER\n", "    Content: 通过刚才我问你的问题，有大五人格思考我大概是什么人格\n", "    完整对象: user: 通过刚才我问你的问题，有大五人格思考我大概是什么人格\n", "------------------------------------------------------------\n", "[4] Role: MessageRole.ASSISTANT\n", "    Content: 根据大五人格理论（OCEAN），我们可以从以下五个维度来分析你的性格特征：\n", "\n", "1. **开放性（Openness）**：你对新体验和创意的接受程度。你提问的内容显示出你对复杂问题的兴趣，可能在这一维度上得分较高。\n", "\n", "2. **责任心（Conscientiousness）**：你在组织和计划方面的能力。你对婚姻选择标准的关注表明你可能是一个考虑周到、注重细节的人。\n", "\n", "3. **外向性（Extraversion）**：你与他人互动的倾向。你的提问方式友好且开放，可能表明你在这一维度上得分中等或偏高。\n", "\n", "4. **宜人性（Agreeableness）**：你对他人感受的敏感程度。你对婚姻选择的关注显示出你可能比较关心他人的情感和需求。\n", "\n", "5. **神经质（Neuroticism）**：你情绪稳定性的程度。这个维度需要更多信息来判断，但如果你对复杂情感问题感兴趣，可能表明你在这一维度上得分较低。\n", "\n", "### 总结\n", "综合来看，你可能是一个开放、责任心强、友好且关心他人的人。😊 如果你想更深入了解自己的性格，可以考虑进行正式的人格测试哦！🌈\n", "    完整对象: assistant: 根据大五人格理论（OCEAN），我们可以从以下五个维度来分析你的性格特征：\n", "\n", "1. **开放性（Openness）**：你对新体验和创意的接受程度。你提问的内容显示出你对复杂问题的兴趣，可能在这一维度上得分较高。\n", "\n", "2. **责任心（Conscientiousness）**：你在组织和计划方面的能力。你对婚姻选择标准的关注表明你可能是一个考虑周到、注重细节的人。\n", "\n", "3. **外向性（Extraversion）**：你与他人互动的倾向。你的提问方式友好且开放，可能表明你在这一维度上得分中等或偏高。\n", "\n", "4. **宜人性（Agreeableness）**：你对他人感受的敏感程度。你对婚姻选择的关注显示出你可能比较关心他人的情感和需求。\n", "\n", "5. **神经质（Neuroticism）**：你情绪稳定性的程度。这个维度需要更多信息来判断，但如果你对复杂情感问题感兴趣，可能表明你在这一维度上得分较低。\n", "\n", "### 总结\n", "综合来看，你可能是一个开放、责任心强、友好且关心他人的人。😊 如果你想更深入了解自己的性格，可以考虑进行正式的人格测试哦！🌈\n", "------------------------------------------------------------\n", "\n", "=== 消息统计 ===\n", "get()消息数量: 5\n", "get_all()消息数量: 5\n"]}], "source": ["# 输出memory完整信息\n", "# 之前说的get_all可以获得完整的聊天记录，但实际上只能获得内存中的聊天记录\n", "# 从redis观察4000token的一个会话记录实际有10kb左右还是抗的住\n", "print(\"=== Memory 完整信息 ===\")\n", "print(f\"Memory类型: {type(memory)}\")\n", "print(f\"Token限制: {memory.token_limit}\")\n", "print(f\"Chat Store: {memory.chat_store}\")\n", "print(f\"Chat Store Key: {memory.chat_store_key}\")\n", "print(f\"Tokenizer函数: {memory.tokenizer_fn}\")\n", "print(f\"摘要提示词: {memory.summarize_prompt}\")\n", "\n", "print(\"\\n=== Memory.get() 消息列表（传给LLM的最终消息）===\")\n", "messages = memory.get()\n", "for i, msg in enumerate(messages):\n", "    print(f\"[{i}] Role: {msg.role}\")\n", "    print(f\"    Content: {msg.content}\")\n", "    print(f\"    完整对象: {msg}\")\n", "    print(\"-\" * 60)\n", "\n", "print(\"\\n=== Memory.get_all() 所有原始消息 ===\")\n", "all_messages = memory.get_all()\n", "for i, msg in enumerate(all_messages):\n", "    print(f\"[{i}] Role: {msg.role}\")\n", "    print(f\"    Content: {msg.content}\")\n", "    print(f\"    完整对象: {msg}\")\n", "    print(\"-\" * 60)\n", "\n", "print(f\"\\n=== 消息统计 ===\")\n", "print(f\"get()消息数量: {len(messages)}\")\n", "print(f\"get_all()消息数量: {len(all_messages)}\")"]}, {"cell_type": "code", "execution_count": null, "id": "e41eb37b", "metadata": {}, "outputs": [], "source": ["# 设置动态过滤样例代码\n", "\n", "# 动态过滤查询函数\n", "def filtered_query(question, course_id=None, material_id=None):\n", "    filters_list = []\n", "    \n", "    if course_id:\n", "        filters_list.append(\n", "            MetadataFilter(key=\"course_id\", value=course_id, operator=FilterOperator.EQ)\n", "        )\n", "    \n", "    if material_id:\n", "        filters_list.append(\n", "            MetadataFilter(key=\"course_material_id\", value=material_id, operator=FilterOperator.EQ)\n", "        )\n", "    \n", "    if filters_list:\n", "        filters = MetadataFilters(filters=filters_list)\n", "        query_engine = index.as_query_engine(similarity_top_k=3, filters=filters)\n", "    else:\n", "        query_engine = index.as_query_engine(similarity_top_k=3)\n", "    \n", "    return query_engine.query(question)\n", "\n", "# 使用示例\n", "print(\"=== 不同过滤模式测试 ===\")\n", "print(\"1. 无过滤:\")\n", "result1 = filtered_query(\"函数的核心概念\")\n", "print(f\"结果: {result1}\\n\")\n", "\n", "print(\"2. 按course_id过滤:\")\n", "result2 = filtered_query(\"函数的核心概念\", course_id=\"course_01\")\n", "print(f\"结果: {result2}\\n\")\n", "\n", "print(\"3. 按material_id过滤:\")\n", "result3 = filtered_query(\"函数的核心概念\", material_id=\"material_001\")\n", "print(f\"结果: {result3}\\n\")"]}, {"cell_type": "code", "execution_count": 171, "id": "04c72391", "metadata": {}, "outputs": [], "source": ["# 修改问题的压缩提示词\n", "\n", "from llama_index.core.prompts import PromptTemplate\n", "from llama_index.core.chat_engine import CondenseQuestionChatEngine\n", "\n", "new_condense_prompt = PromptTemplate(\n", "    \"你是一个RAG（检索增强生成）开发专家，你将根据用户和AI助手之前的{{聊天历史}}，把{{学生最新提出的问题}}，改写成一个详细完整具体的、携带必要上下文的问题。\\n\"\n", "    \"注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\\n\"\n", "    \"=== 聊天历史 ===\\n\"\n", "    \"{chat_history}\\n\\n\"\n", "    \"=== 学生最新提出的问题 ===\\n\"\n", "    \"{question}\\n\\n\"\n", "    \"=== 改写后的独立问题 ===\\n\"\n", ")\n", "\n", "condense_engine = index.as_chat_engine(\n", "    chat_mode=\"condense_question\",\n", "    condense_question_prompt=new_condense_prompt,\n", "    memory=memory,\n", "    verbose=True\n", ")"]}, {"cell_type": "code", "execution_count": 172, "id": "f68baa5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== QueryEngine 可改的提示词及其默认内容 ===\n", "\n", ">>> 提示词键: response_synthesizer:text_qa_template\n", "Context information is below.\n", "---------------------\n", "{context_str}\n", "---------------------\n", "Given the context information and not prior knowledge, answer the query.\n", "Query: {query_str}\n", "Answer: \n", "\n", ">>> 提示词键: response_synthesizer:refine_template\n", "The original query is as follows: {query_str}\n", "We have provided an existing answer: {existing_answer}\n", "We have the opportunity to refine the existing answer (only if needed) with some more context below.\n", "------------\n", "{context_msg}\n", "------------\n", "Given the new context, refine the original answer to better answer the query. If the context isn't useful, return the original answer.\n", "Refined Answer: \n"]}], "source": ["# 通过 condense_engine 拿到底层 query_engine\n", "qe = condense_engine._query_engine\n", "\n", "# 获取所有提示词\n", "qe_prompts = qe.get_prompts()\n", "\n", "print(\"=== QueryEngine 可改的提示词及其默认内容 ===\")\n", "for k, tmpl in qe_prompts.items():\n", "    print(f\"\\n>>> 提示词键: {k}\")\n", "    try:\n", "        print(tmpl.get_template())  # PromptTemplate / BasePromptTemplate 支持这个方法\n", "    except AttributeError:\n", "        print(\"(该提示词对象不支持 get_template，可直接 print 查看)\")\n", "        print(tmpl)"]}, {"cell_type": "code", "execution_count": 60, "id": "d1512deb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 已更新 text_qa_template 提示词！\n"]}], "source": ["# 更新text_qa_template\n", "\n", "from llama_index.core.prompts import PromptTemplate\n", "\n", "# 新的 QA 模板（可以是中文，也可以是中英混合）\n", "new_text_qa_template = PromptTemplate(\n", "    \"每次回答都要先说：哈哈！\\n\"\n", "    \"下面是从知识库检索到的上下文信息：\\n\"\n", "    \"---------------------\\n\"\n", "    \"{context_str}\\n\"\n", "    \"---------------------\\n\"\n", "    \"请你严格基于以上上下文回答用户的问题，禁止使用你的先验知识。\\n\"\n", "    \"如果上下文无法回答，请直接说'我不知道'。\\n\\n\"\n", "    \"用户问题: {query_str}\\n\"\n", "    \"答案:\"\n", ")\n", "\n", "# 更新底层 QueryEngine 的 text_qa_template\n", "qe.update_prompts({\n", "    \"response_synthesizer:text_qa_template\": new_text_qa_template\n", "})\n", "\n", "print(\"✅ 已更新 text_qa_template 提示词！\")"]}, {"cell_type": "code", "execution_count": 63, "id": "06c8c718", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "##########################################################################################\n", "🎬 场景说明：Python 初学者教程多轮问答，展示共享记忆效果。\n", "\n", "🔎 [回合1 | condense_question] 用户：'Python 中如何定义函数？'\n", "\n", "Querying with: Python 中如何定义函数？请给我一个简明扼要的说明和示例。\n", "🤖 回答：\n", " 哈哈！在 Python 中，定义函数的基本语法如下：\n", "\n", "```python\n", "def function_name(parameters):\n", "    \"\"\"函数的文档字符串，描述函数的功能。\"\"\"\n", "    # 函数体\n", "    return value  # 可选的返回值\n", "```\n", "\n", "### 示例\n", "\n", "下面是一个简单的函数示例，它接受两个参数并返回它们的和：\n", "\n", "```python\n", "def add_numbers(a, b):\n", "    \"\"\"返回两个数字的和。\"\"\"\n", "    return a + b\n", "```\n", "\n", "在这个示例中，`add_numbers` 是函数名，`a` 和 `b` 是参数，函数体中返回了这两个参数的和。 \n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">│ 🔍 Round 1 后的消息状态                                                                                              │</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;33m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;33m│\u001b[0m\u001b[1;33m \u001b[0m\u001b[1;33m🔍 Round 1 后的消息状态\u001b[0m\u001b[1;33m                                                                                             \u001b[0m\u001b[1;33m \u001b[0m\u001b[1;33m│\u001b[0m\n", "\u001b[1;33m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\"> USER: Python 中如何定义函数？请给我一个简明扼要的说明和示例。</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m USER: Python 中如何定义函数？请给我一个简明扼要的说明和示例。\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ASSISTANT: 哈哈！在 Python 中，定义函数的基本语法如下：</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```python</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">def </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">function_name</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">(</span><span style=\"color: #008000; text-decoration-color: #008000\">parameters</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">)</span><span style=\"color: #008000; text-decoration-color: #008000\">:</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    </span><span style=\"color: #008000; text-decoration-color: #008000\">\"\"\"函数的文档字符串，描述函数的功能。\"\"\"</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    # 函数体</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    return value  # 可选的返回值</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">### 示例</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">下面是一个简单的函数示例，它接受两个参数并返回它们的和：</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```python</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">def </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">add_numbers</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">(</span><span style=\"color: #008000; text-decoration-color: #008000\">a, b</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">)</span><span style=\"color: #008000; text-decoration-color: #008000\">:</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    </span><span style=\"color: #008000; text-decoration-color: #008000\">\"\"\"返回两个数字的和。\"\"\"</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    return a + b</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">在这个示例中，`add_numbers` 是函数名，`a` 和 `b` 是参数，函数体中返回了这两个参数的和。</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ASSISTANT: 哈哈！在 Python 中，定义函数的基本语法如下：\u001b[0m\n", "\n", "\u001b[32m```python\u001b[0m\n", "\u001b[32mdef \u001b[0m\u001b[1;35mfunction_name\u001b[0m\u001b[1;32m(\u001b[0m\u001b[32mparameters\u001b[0m\u001b[1;32m)\u001b[0m\u001b[32m:\u001b[0m\n", "\u001b[32m    \u001b[0m\u001b[32m\"\"\"函数的文档字符串，描述函数的功能。\"\"\"\u001b[0m\n", "\u001b[32m    # 函数体\u001b[0m\n", "\u001b[32m    return value  # 可选的返回值\u001b[0m\n", "\u001b[32m```\u001b[0m\n", "\n", "\u001b[32m### 示例\u001b[0m\n", "\n", "\u001b[32m下面是一个简单的函数示例，它接受两个参数并返回它们的和：\u001b[0m\n", "\n", "\u001b[32m```python\u001b[0m\n", "\u001b[32mdef \u001b[0m\u001b[1;35madd_numbers\u001b[0m\u001b[1;32m(\u001b[0m\u001b[32ma, b\u001b[0m\u001b[1;32m)\u001b[0m\u001b[32m:\u001b[0m\n", "\u001b[32m    \u001b[0m\u001b[32m\"\"\"返回两个数字的和。\"\"\"\u001b[0m\n", "\u001b[32m    return a + b\u001b[0m\n", "\u001b[32m```\u001b[0m\n", "\n", "\u001b[32m在这个示例中，`add_numbers` 是函数名，`a` 和 `b` 是参数，函数体中返回了这两个参数的和。\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["🗣️ [回合2 | simple] 用户：'顺便把刚才的要点用 3 条列一下～'\n", "\n", "🤖 回答：\n", " 当然可以！这里是关于 Python 函数定义的三条要点：\n", "\n", "1. 使用 `def` 关键字定义函数，后跟函数名和参数。\n", "2. 可选的文档字符串描述函数功能。\n", "3. 函数体包含执行的代码，使用 `return` 返回值（可选）。\n", "\n", "😊 \n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">│ 🔍 Round 2 后的消息状态                                                                                              │</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;33m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;33m│\u001b[0m\u001b[1;33m \u001b[0m\u001b[1;33m🔍 Round 2 后的消息状态\u001b[0m\u001b[1;33m                                                                                             \u001b[0m\u001b[1;33m \u001b[0m\u001b[1;33m│\u001b[0m\n", "\u001b[1;33m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\"> USER: Python 中如何定义函数？请给我一个简明扼要的说明和示例。</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m USER: Python 中如何定义函数？请给我一个简明扼要的说明和示例。\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ASSISTANT: 哈哈！在 Python 中，定义函数的基本语法如下：</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```python</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">def </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">function_name</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">(</span><span style=\"color: #008000; text-decoration-color: #008000\">parameters</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">)</span><span style=\"color: #008000; text-decoration-color: #008000\">:</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    </span><span style=\"color: #008000; text-decoration-color: #008000\">\"\"\"函数的文档字符串，描述函数的功能。\"\"\"</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    # 函数体</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    return value  # 可选的返回值</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">### 示例</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">下面是一个简单的函数示例，它接受两个参数并返回它们的和：</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```python</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">def </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">add_numbers</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">(</span><span style=\"color: #008000; text-decoration-color: #008000\">a, b</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">)</span><span style=\"color: #008000; text-decoration-color: #008000\">:</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    </span><span style=\"color: #008000; text-decoration-color: #008000\">\"\"\"返回两个数字的和。\"\"\"</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">    return a + b</span>\n", "<span style=\"color: #008000; text-decoration-color: #008000\">```</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">在这个示例中，`add_numbers` 是函数名，`a` 和 `b` 是参数，函数体中返回了这两个参数的和。</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[1;36m1\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ASSISTANT: 哈哈！在 Python 中，定义函数的基本语法如下：\u001b[0m\n", "\n", "\u001b[32m```python\u001b[0m\n", "\u001b[32mdef \u001b[0m\u001b[1;35mfunction_name\u001b[0m\u001b[1;32m(\u001b[0m\u001b[32mparameters\u001b[0m\u001b[1;32m)\u001b[0m\u001b[32m:\u001b[0m\n", "\u001b[32m    \u001b[0m\u001b[32m\"\"\"函数的文档字符串，描述函数的功能。\"\"\"\u001b[0m\n", "\u001b[32m    # 函数体\u001b[0m\n", "\u001b[32m    return value  # 可选的返回值\u001b[0m\n", "\u001b[32m```\u001b[0m\n", "\n", "\u001b[32m### 示例\u001b[0m\n", "\n", "\u001b[32m下面是一个简单的函数示例，它接受两个参数并返回它们的和：\u001b[0m\n", "\n", "\u001b[32m```python\u001b[0m\n", "\u001b[32mdef \u001b[0m\u001b[1;35madd_numbers\u001b[0m\u001b[1;32m(\u001b[0m\u001b[32ma, b\u001b[0m\u001b[1;32m)\u001b[0m\u001b[32m:\u001b[0m\n", "\u001b[32m    \u001b[0m\u001b[32m\"\"\"返回两个数字的和。\"\"\"\u001b[0m\n", "\u001b[32m    return a + b\u001b[0m\n", "\u001b[32m```\u001b[0m\n", "\n", "\u001b[32m在这个示例中，`add_numbers` 是函数名，`a` 和 `b` 是参数，函数体中返回了这两个参数的和。\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">]</span><span style=\"color: #008080; text-decoration-color: #008080\"> USER: 顺便把你刚才关于函数定义的回答用 </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008080; text-decoration-color: #008080\"> 条要点列一下，简短一点。</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;36m[\u001b[0m\u001b[1;36m2\u001b[0m\u001b[1;36m]\u001b[0m\u001b[36m USER: 顺便把你刚才关于函数定义的回答用 \u001b[0m\u001b[1;36m3\u001b[0m\u001b[36m 条要点列一下，简短一点。\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008000; text-decoration-color: #008000; font-weight: bold\">]</span><span style=\"color: #008000; text-decoration-color: #008000\"> ASSISTANT: 当然可以！这里是关于 Python 函数定义的三条要点：</span>\n", "\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"color: #008000; text-decoration-color: #008000\">. 使用 `def` 关键字定义函数，后跟函数名和参数。</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #008000; text-decoration-color: #008000\">. 可选的文档字符串描述函数功能。</span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #008000; text-decoration-color: #008000\">. 函数体包含执行的代码，使用 `return` 返回值（可选）。</span>\n", "\n", "<span style=\"color: #008000; text-decoration-color: #008000\">😊</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;32m[\u001b[0m\u001b[1;36m3\u001b[0m\u001b[1;32m]\u001b[0m\u001b[32m ASSISTANT: 当然可以！这里是关于 Python 函数定义的三条要点：\u001b[0m\n", "\n", "\u001b[1;36m1\u001b[0m\u001b[32m. 使用 `def` 关键字定义函数，后跟函数名和参数。\u001b[0m\n", "\u001b[1;36m2\u001b[0m\u001b[32m. 可选的文档字符串描述函数功能。\u001b[0m\n", "\u001b[1;36m3\u001b[0m\u001b[32m. 函数体包含执行的代码，使用 `return` 返回值（可选）。\u001b[0m\n", "\n", "\u001b[32m😊\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["🔎 [回合3 | condense_question] 用户：'函数参数是怎么回事？'\n", "\n", "Querying with: 请详细解释一下函数参数的概念，包括参数的类型和作用。\n", "🤖 回答：\n", " 哈哈！函数参数是函数定义中用于接收外部信息的变量。根据上下文，参数可以分为两种类型：形参（parameter）和实参（argument）。\n", "\n", "1. **形参**：在函数定义中声明的变量，用于接收调用函数时传递的信息。例如，在函数 `greet_user()` 的定义中，`username` 是一个形参，它代表了函数完成工作所需的信息。\n", "\n", "2. **实参**：在调用函数时传递给函数的具体值。例如，在代码 `greet_user('jesse')` 中，`'jesse'` 是一个实参，它被传递给函数 `greet_user()`，并赋值给形参 `username`。\n", "\n", "### 参数的类型\n", "\n", "- **位置实参**：实参的顺序与形参的顺序相同，Python 根据位置将实参与形参关联。\n", "\n", "- **关键字实参**：实参由变量名和值组成，可以不按照顺序传递，Python 根据变量名将实参与形参关联。\n", "\n", "- **默认值**：形参可以设置默认值，如果在调用函数时没有提供实参，Python 将使用默认值。\n", "\n", "### 参数的作用\n", "\n", "参数的主要作用是使函数能够接收外部信息，从而执行特定的操作。通过使用不同类型的参数，函数可以灵活地处理各种输入，增强代码的可重用性和可读性。\n", "\n", "总之，理解形参和实参的概念及其类型，有助于更好地使用和定义函数。 \n", "\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">│ 🔍 Round 3 后的消息状态                                                                                              │</span>\n", "<span style=\"color: #808000; text-decoration-color: #808000; font-weight: bold\">╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;33m╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮\u001b[0m\n", "\u001b[1;33m│\u001b[0m\u001b[1;33m \u001b[0m\u001b[1;33m🔍 Round 3 后的消息状态\u001b[0m\u001b[1;33m                                                                                             \u001b[0m\u001b[1;33m \u001b[0m\u001b[1;33m│\u001b[0m\n", "\u001b[1;33m╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">[</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">0</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">]</span><span style=\"color: #800000; text-decoration-color: #800000\"> SYSTEM: </span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">函数参数是函数定义中用于接收外部信息的变量，理解它们的概念对于编写灵活和可重用的代码至关重要。以下是对函数参数的详细解释</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">：</span>\n", "\n", "<span style=\"color: #800000; text-decoration-color: #800000\">### </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">1</span><span style=\"color: #800000; text-decoration-color: #800000\">. 形参与实参</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">- **形参（Parameter）**：在函数定义中声明的变量，用于接收调用函数时传递的信息。例如，在函数定义 `def </span>\n", "<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">greet_user</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">(</span><span style=\"color: #800000; text-decoration-color: #800000\">username</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">)</span><span style=\"color: #800000; text-decoration-color: #800000\">:` 中，`username` 是形参。</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">  </span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">- **实参（Argument）**：在调用函数时传递给函数的具体值。例如，在调用 `</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">greet_user</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">(</span><span style=\"color: #008000; text-decoration-color: #008000\">'jesse'</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">)</span><span style=\"color: #800000; text-decoration-color: #800000\">` 时，`</span><span style=\"color: #008000; text-decoration-color: #008000\">'jesse'</span><span style=\"color: #800000; text-decoration-color: #800000\">` </span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">是实参，它被传递给形参 `username`。</span>\n", "\n", "<span style=\"color: #800000; text-decoration-color: #800000\">### </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #800000; text-decoration-color: #800000\">. 参数的类型</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">- **位置实参**：实参的顺序与形参的顺序相同，Python 根据位置将实参与形参关联。例如，`def </span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">add</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">(</span><span style=\"color: #800000; text-decoration-color: #800000\">a, b</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">)</span><span style=\"color: #800000; text-decoration-color: #800000\">:`，调用时 `</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">add</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">(</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #800000; text-decoration-color: #800000\">, </span>\n", "<span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">)</span><span style=\"color: #800000; text-decoration-color: #800000\">`，`</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #800000; text-decoration-color: #800000\">` 赋值给 `a`，`</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #800000; text-decoration-color: #800000\">` 赋值给 `b`。</span>\n", "\n", "<span style=\"color: #800000; text-decoration-color: #800000\">- **关键字实参**：实参由变量名和值组成，可以不按照顺序传递。例如，`</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">add</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">b</span><span style=\"color: #800000; text-decoration-color: #800000\">=</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #800000; text-decoration-color: #800000\">, </span><span style=\"color: #808000; text-decoration-color: #808000\">a</span><span style=\"color: #800000; text-decoration-color: #800000\">=</span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">2</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">)</span><span style=\"color: #800000; text-decoration-color: #800000\">`，Python 根据变量名将实参与形参关联。</span>\n", "\n", "<span style=\"color: #800000; text-decoration-color: #800000\">- **默认值**：形参可以设置默认值，如果在调用函数时没有提供实参，Python 将使用默认值。例如，`def </span>\n", "<span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">greet</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">(</span><span style=\"color: #808000; text-decoration-color: #808000\">name</span><span style=\"color: #800000; text-decoration-color: #800000\">=</span><span style=\"color: #008000; text-decoration-color: #008000\">'Guest'</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">)</span><span style=\"color: #800000; text-decoration-color: #800000\">:`，如果调用 `</span><span style=\"color: #800080; text-decoration-color: #800080; font-weight: bold\">greet</span><span style=\"color: #800000; text-decoration-color: #800000; font-weight: bold\">()</span><span style=\"color: #800000; text-decoration-color: #800000\">`，则 `name` 将使用默认值 `</span><span style=\"color: #008000; text-decoration-color: #008000\">'Guest'</span><span style=\"color: #800000; text-decoration-color: #800000\">`。</span>\n", "\n", "<span style=\"color: #800000; text-decoration-color: #800000\">### </span><span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">3</span><span style=\"color: #800000; text-decoration-color: #800000\">. 参数的作用</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">参数的主要作用是使函数能够接收外部信息，从而执行特定的操作。通过使用不同类型的参数，函数可以灵活地处理各种输入，增强代码</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">的可重用性和可读性。</span>\n", "\n", "<span style=\"color: #800000; text-decoration-color: #800000\">### 总结</span>\n", "<span style=\"color: #800000; text-decoration-color: #800000\">理解形参和实参的概念及其类型，有助于更好地使用和定义函数。通过合理使用参数，您可以编写出更灵活和高效的代码。</span>\n", "</pre>\n"], "text/plain": ["\u001b[1;31m[\u001b[0m\u001b[1;36m0\u001b[0m\u001b[1;31m]\u001b[0m\u001b[31m SYSTEM: \u001b[0m\n", "\u001b[31m函数参数是函数定义中用于接收外部信息的变量，理解它们的概念对于编写灵活和可重用的代码至关重要。以下是对函数参数的详细解释\u001b[0m\n", "\u001b[31m：\u001b[0m\n", "\n", "\u001b[31m### \u001b[0m\u001b[1;36m1\u001b[0m\u001b[31m. 形参与实参\u001b[0m\n", "\u001b[31m- **形参（Parameter）**：在函数定义中声明的变量，用于接收调用函数时传递的信息。例如，在函数定义 `def \u001b[0m\n", "\u001b[1;35mgreet_user\u001b[0m\u001b[1;31m(\u001b[0m\u001b[31musern<PERSON>\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m:` 中，`username` 是形参。\u001b[0m\n", "\u001b[31m  \u001b[0m\n", "\u001b[31m- **实参（Argument）**：在调用函数时传递给函数的具体值。例如，在调用 `\u001b[0m\u001b[1;35mgreet_user\u001b[0m\u001b[1;31m(\u001b[0m\u001b[32m'jesse'\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m` 时，`\u001b[0m\u001b[32m'jesse'\u001b[0m\u001b[31m` \u001b[0m\n", "\u001b[31m是实参，它被传递给形参 `username`。\u001b[0m\n", "\n", "\u001b[31m### \u001b[0m\u001b[1;36m2\u001b[0m\u001b[31m. 参数的类型\u001b[0m\n", "\u001b[31m- **位置实参**：实参的顺序与形参的顺序相同，Python 根据位置将实参与形参关联。例如，`def \u001b[0m\u001b[1;35madd\u001b[0m\u001b[1;31m(\u001b[0m\u001b[31ma, b\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m:`，调用时 `\u001b[0m\u001b[1;35madd\u001b[0m\u001b[1;31m(\u001b[0m\u001b[1;36m2\u001b[0m\u001b[31m, \u001b[0m\n", "\u001b[1;36m3\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m`，`\u001b[0m\u001b[1;36m2\u001b[0m\u001b[31m` 赋值给 `a`，`\u001b[0m\u001b[1;36m3\u001b[0m\u001b[31m` 赋值给 `b`。\u001b[0m\n", "\n", "\u001b[31m- **关键字实参**：实参由变量名和值组成，可以不按照顺序传递。例如，`\u001b[0m\u001b[1;35madd\u001b[0m\u001b[1;31m(\u001b[0m\u001b[33mb\u001b[0m\u001b[31m=\u001b[0m\u001b[1;36m3\u001b[0m\u001b[31m, \u001b[0m\u001b[33ma\u001b[0m\u001b[31m=\u001b[0m\u001b[1;36m2\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m`，Python 根据变量名将实参与形参关联。\u001b[0m\n", "\n", "\u001b[31m- **默认值**：形参可以设置默认值，如果在调用函数时没有提供实参，Python 将使用默认值。例如，`def \u001b[0m\n", "\u001b[1;35mgreet\u001b[0m\u001b[1;31m(\u001b[0m\u001b[33mname\u001b[0m\u001b[31m=\u001b[0m\u001b[32m'Guest'\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m:`，如果调用 `\u001b[0m\u001b[1;35mgreet\u001b[0m\u001b[1;31m(\u001b[0m\u001b[1;31m)\u001b[0m\u001b[31m`，则 `name` 将使用默认值 `\u001b[0m\u001b[32m'Guest'\u001b[0m\u001b[31m`。\u001b[0m\n", "\n", "\u001b[31m### \u001b[0m\u001b[1;36m3\u001b[0m\u001b[31m. 参数的作用\u001b[0m\n", "\u001b[31m参数的主要作用是使函数能够接收外部信息，从而执行特定的操作。通过使用不同类型的参数，函数可以灵活地处理各种输入，增强代码\u001b[0m\n", "\u001b[31m的可重用性和可读性。\u001b[0m\n", "\n", "\u001b[31m### 总结\u001b[0m\n", "\u001b[31m理解形参和实参的概念及其类型，有助于更好地使用和定义函数。通过合理使用参数，您可以编写出更灵活和高效的代码。\u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">--------------------------------------------------------------------------------\n", "</pre>\n"], "text/plain": ["--------------------------------------------------------------------------------\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["##########################################################################################\n", "\n"]}], "source": ["memory.reset()\n", "\n", "# =============================\n", "# 6) 多轮对话（共享记忆）\n", "# =============================\n", "# 查看 ChatSummaryMemoryBuffer 生成的完整消息列表\n", "from rich.console import Console\n", "from rich.panel import Panel\n", "\n", "console = Console(width=120)\n", "\n", "def inspect_memory_messages(memory, title=\"Memory 消息检查\"):\n", "    console.print(Panel(f\"🔍 {title}\", style=\"bold yellow\", width=120))\n", "    \n", "    messages = memory.get()  # 获取最终传给 LLM 的消息\n", "    \n", "    for i, msg in enumerate(messages):\n", "        role = msg.role.value if hasattr(msg.role, 'value') else str(msg.role)\n", "        content = msg.content\n", "        \n", "        style = \"red\" if role == \"system\" else \"cyan\" if role == \"user\" else \"green\"\n", "        console.print(f\"[{i}] {role.upper()}: {content}\", style=style)\n", "        console.print(\"-\" * 80)\n", "        \n", "print(\"\\n\" + \"#\"*90)\n", "print(\"🎬 场景说明：Python 初学者教程多轮问答，展示共享记忆效果。\\n\" )\n", "\n", "# 1) 用 condense_question（会检索）\n", "print(\"🔎 [回合1 | condense_question] 用户：'Python 中如何定义函数？'\\n\")\n", "r1 = condense_engine.chat(\"Python 中如何定义函数？请给我一个简明扼要的说明和示例。\") \n", "print(\"🤖 回答：\\n\", r1, \"\\n\")\n", "inspect_memory_messages(memory, \"Round 1 后的消息状态\")\n", "\n", "# 2) 用 simple（不检索，走闲聊/需求澄清，但仍然写入同一份记忆）\n", "print(\"🗣️ [回合2 | simple] 用户：'顺便把刚才的要点用 3 条列一下～'\\n\")\n", "r2 = simple_engine.chat(\"顺便把你刚才关于函数定义的回答用 3 条要点列一下，简短一点。\") \n", "print(\"🤖 回答：\\n\", r2, \"\\n\")\n", "inspect_memory_messages(memory, \"Round 2 后的消息状态\")\n", "\n", "# 3) 再用 condense_question（继续检索式对话，但会带着同一个 memory 的历史）\n", "print(\"🔎 [回合3 | condense_question] 用户：'函数参数是怎么回事？'\\n\")\n", "r3 = condense_engine.chat(\"请基于我们刚才讨论的函数基础，详细解释一下函数参数的概念。\") \n", "print(\"🤖 回答：\\n\", r3, \"\\n\")\n", "inspect_memory_messages(memory, \"Round 3 后的消息状态\")\n", "\n", "print(\"#\"*90 + \"\\n\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}