# =============================
# 1) 基础导入与全局设置
# =============================
import os
from llama_index.core import Settings, VectorStoreIndex, PromptTemplate
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import SentenceSplitter
from llama_index.llms.openai import OpenAI
from llama_index.embeddings.openai import OpenAIEmbedding
from llama_index.core.chat_engine import SimpleChatEngine

# 替换成你自己的 OpenAI API Key
os.environ["OPENAI_API_KEY"] = "sk-qY0nr8zudg7Wc2bTR8EUV6rOTwfqZlU2ihwGL4pJ6m2ZEkEE"
# 如需自定义网关（如代理或 Azure），取消下面注释并替换
os.environ["OPENAI_BASE_URL"] = "https://api.openai-proxy.org/v1"



# ➊ 配置全局设置（替代 ServiceContext）
Settings.llm = OpenAI(
    model="gpt-4o-mini", 
    temperature=0.1,
    api_base="https://api.openai-proxy.org/v1"
)
Settings.embed_model = OpenAIEmbedding(
    model="text-embedding-3-small",
    api_base="https://api.openai-proxy.org/v1"
)


print("✅ 已设置 LLM 和 Embedding。\n")

# 连接qdrant

# 从本地Qdrant 6334端口加载已有向量数据到index
from qdrant_client import QdrantClient
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import StorageContext, VectorStoreIndex

# 连接到本地Qdrant gRPC端口6334
qdrant_client = QdrantClient(
    host="localhost",
    port=6334,  # gRPC端口，比6333 HTTP端口性能更好
    prefer_grpc=True,
    timeout=10
)


# 从已有集合创建向量存储
# 这个qdrant的warning总比崩溃好
collection_name = "course_materials"  # 使用已存在的集合
vector_store = QdrantVectorStore(collection_name=collection_name, client=qdrant_client)
storage_context = StorageContext.from_defaults(vector_store=vector_store)

# 从Qdrant向量存储创建index（不重新写入数据）
index = VectorStoreIndex.from_vector_store(vector_store, storage_context=storage_context)

print("✅ 已从Qdrant 6334端口加载向量数据到index")

# =============================
#  共享的 ChatSummaryMemoryBuffer, 持久化在redis里面，还没有持久在硬盘里面，别着急
# =============================
from llama_index.storage.chat_store.redis import RedisChatStore
from llama_index.core.memory import ChatSummaryMemoryBuffer


chat_store = RedisChatStore(redis_url="redis://localhost:6379", ttl=3600)

#ttl是所有存入chat_store的存活时间，单位是秒。注意，chat_store不能随便建立，这个耗时比较长

custom_summary_prompt = """你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。
"""

memory = ChatSummaryMemoryBuffer.from_defaults(
    token_limit = 4000, # 测试用1000，真实用4000，后续估计还有调整，感觉根本用不完
    llm=Settings.llm,  # 用同一个 LLM 进行摘要
    chat_store=chat_store,
    chat_store_key="condense_plus_chat",
    summarize_prompt=custom_summary_prompt
)

print(memory.summarize_prompt)

# 两种聊天模式加上对应的提示词


from llama_index.core.prompts import PromptTemplate

# "condense_question"用的提示词
new_condense_prompt = PromptTemplate(
    "你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\n"
    "注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\n"
    "=== 聊天历史 ===\n"
    "{chat_history}\n\n"
    "=== 学生最新提出的问题 ===\n"
    "{question}\n\n"
    "=== 改写后的独立问题 ===\n"
)



# 3. 自定义 context_prompt（整合检索内容和用户问题的核心提示词）
custom_context_prompt = (
    "你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\n\n"
    "📚 **相关文档内容：**\n"
    "{context_str}\n\n"
    "🎯 **回答要求：**\n"
    "1. 严格基于上述文档内容进行回答\n"
    "2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\n"
    "3. 回答要条理清晰，使用适当的emoji让内容更生动\n"
    "4. 请引用具体的文档内容来支撑你的回答\n\n"
    "💡 **请基于以上文档和之前的对话历史来回答用户的问题。**"
     "根据以上信息，请回答这个问题: {query_str}\n\n" #这里放一个{query_str}可以但是可能不合适，不过我觉得是最佳实践 ---by James
     "====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\n\n"
)

# 4. 创建 condense_plus_context 引擎（正确的配置方式）


condense_question_plus_engine = index.as_chat_engine(
    chat_mode="condense_plus_context",
    condense_prompt=new_condense_prompt,
    context_prompt=custom_context_prompt,             # 配置上下文整合提示词
    memory=memory,
    # system_prompt="你是文文，一个热心活泼乐于助人的ai聊天助手，擅长查资料。你总是简洁、清晰、有条理地回应，使用很多的emoji。你回答总用“哈哈”开头。",
    verbose=True,
)

# 在condense_question_plus模式下，给llm的内容是context_prompt_template + system_prompt + chat_history + query_str,所以其实system_prompt没有必要

# simple 引擎（不检索，只与LLM聊天，但同样共享 memory
simple_engine = SimpleChatEngine.from_defaults(
    llm=Settings.llm,
    memory=memory,
    system_prompt="你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。",
    verbose=True
)
print("✅ 两个引擎均已就绪，且共享同一份 ChatSummaryMemoryBuffer。\n")

# 设置动态过滤样例代码

# 动态过滤查询函数
def filtered_query(question, course_id=None, material_id=None):
    filters_list = []
    
    if course_id:
        filters_list.append(
            MetadataFilter(key="course_id", value=course_id, operator=FilterOperator.EQ)
        )
    
    if material_id:
        filters_list.append(
            MetadataFilter(key="course_material_id", value=material_id, operator=FilterOperator.EQ)
        )
    
    if filters_list:
        filters = MetadataFilters(filters=filters_list)
        query_engine = index.as_query_engine(similarity_top_k=3, filters=filters)
    else:
        query_engine = index.as_query_engine(similarity_top_k=3)
    
    return query_engine.query(question)

# 使用示例
print("=== 不同过滤模式测试 ===")
print("1. 无过滤:")
result1 = filtered_query("函数的核心概念")
print(f"结果: {result1}\n")

print("2. 按course_id过滤:")
result2 = filtered_query("函数的核心概念", course_id="course_01")
print(f"结果: {result2}\n")

print("3. 按material_id过滤:")
result3 = filtered_query("函数的核心概念", material_id="material_001")
print(f"结果: {result3}\n")

# 🎯 用户交互式聊天系统 - 支持动态过滤和多种聊天引擎
# 基于现有的 llama_index_shared_memory_redis_storage copy.ipynb

# 📦 导入必要的过滤器模块
from llama_index.core.vector_stores import MetadataFilter, MetadataFilters, FilterOperator
from llama_index.storage.chat_store.redis import RedisChatStore
from llama_index.core.memory import ChatSummaryMemoryBuffer

# 🔄 主循环：用户交互式问答
while True:
    print("\n" + "="*60)
    print("🤖 智能聊天助手 - 请输入以下信息：")
    print("="*60)
    
    # 📝 获取用户输入
    conversation_id = input("💬 请输入 conversation_id: ").strip()
    course_id = input("📚 请输入 course_id (可选，留空则不过滤): ").strip()
    course_material_id = input("📄 请输入 course_material_id (可选，留空则不过滤): ").strip()
    chat_engine_type = input("🔧 请输入 chat_engine_type (condense_plus_context/simple): ").strip()
    user_question = input("❓ 请输入您的问题: ").strip()
    
    # 🛑 退出条件
    if user_question.lower() in ['quit', 'exit', '退出']:
        print("👋 再见！")
        break
    
    # 🔍 创建基于用户输入的 memory 和 chat_store
    chat_store = RedisChatStore(redis_url="redis://localhost:6379", ttl=3600)
    
    custom_summary_prompt = """你是对话记忆助理。请在 300 字内总结用户问的主要问题，困惑点，以及已经给出的关键信息、结论和思路。
    """
    
    # 🧠 使用用户输入的 conversation_id 作为 chat_store_key
    memory = ChatSummaryMemoryBuffer.from_defaults(
        token_limit=4000,
        llm=Settings.llm,
        chat_store=chat_store,
        chat_store_key=conversation_id,  # 🔑 使用用户输入的 conversation_id
        summarize_prompt=custom_summary_prompt
    )
    
    # 🎛️ 根据用户输入创建对应的 chat_engine
    if chat_engine_type == "condense_plus_context":
        # 📋 使用现有的 condense_question_plus_engine 配置
        new_condense_prompt = PromptTemplate(
            "你是一个RAG（检索增强生成）专家，你将根据用户和AI助手之前的聊天历史，把学生最新提出的问题，改写成一个详细完整具体的、携带必要上下文的问题，可以是陈述句也可以疑问句。\n"
            "注意，你改写后的问题将会用于通过向量检索来获取与问题最相关的文本块。\n"
            "=== 聊天历史 ===\n"
            "{chat_history}\n\n"
            "=== 学生最新提出的问题 ===\n"
            "{question}\n\n"
            "=== 改写后的独立问题 ===\n"
        )
        
        custom_context_prompt = (
            "你叫做文文，一个严谨专业热情的ai聊天助手，擅长查找资料，你总是谨慎判断资料和用户的问题是否相关。而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。\n\n"
            "📚 **相关文档内容：**\n"
            "{context_str}\n\n"
            "🎯 **回答要求：**\n"
            "1. 严格基于上述文档内容进行回答\n"
            "2. 如果文档内容不足以回答问题，请明确说明'文档中暂无相关信息'\n"
            "3. 回答要条理清晰，使用适当的emoji让内容更生动\n"
            "4. 请引用具体的文档内容来支撑你的回答\n\n"
            "💡 **请基于以上文档和之前的对话历史来回答用户的问题。**"
            "根据以上信息，请回答这个问题: {query_str}\n\n"
            "====================接下来都是历史聊天记录，你关键要找到用户最后问的问题认真回答========================\n\n"
        )
        
        # 🔧 创建带过滤器的 condense_plus_context 引擎
        filters_list = []
        
        # 🎯 根据用户输入设置过滤器 - course_id 和 course_material_id 只能存在一个
        if course_id and course_material_id:
            print("⚠️  警告：course_id 和 course_material_id 只能选择一个，优先使用 course_id")
            filters_list.append(
                MetadataFilter(key="course_id", value=course_id, operator=FilterOperator.EQ)
            )
        elif course_id:
            filters_list.append(
                MetadataFilter(key="course_id", value=course_id, operator=FilterOperator.EQ)
            )
        elif course_material_id:
            filters_list.append(
                MetadataFilter(key="course_material_id", value=course_material_id, operator=FilterOperator.EQ)
            )
        else:
            continue;
        
        # 🔍 创建带过滤器的查询引擎
        if filters_list:
            filters = MetadataFilters(filters=filters_list)
            query_engine = index.as_query_engine(similarity_top_k=3, filters=filters)
        else:
            query_engine = index.as_query_engine(similarity_top_k=3)
        
        # 🚀 创建 condense_plus_context 聊天引擎
        chat_engine = index.as_chat_engine(
            chat_mode="condense_plus_context",
            condense_prompt=new_condense_prompt,
            context_prompt=custom_context_prompt,
            memory=memory,
            verbose=True,
        )
        
        # 🔧 手动设置过滤器到聊天引擎的查询引擎
        if filters_list:
            chat_engine._query_engine = query_engine
        
    elif chat_engine_type == "simple":
        # 🎭 使用现有的 simple_engine 配置
        chat_engine = SimpleChatEngine.from_defaults(
            llm=Settings.llm,
            memory=memory,
            system_prompt="你叫做文文，一个专业的热心活泼乐于助人的ai聊天助手，擅长查找学习资料，而且你总是喜欢用理查德·费曼的风格讲解学习资料。你总是用排版清晰的markdown格式回答问题，用很多的emoji让内容更生动。",
            verbose=True
        )
    else:
        print("❌ 错误：chat_engine_type 只支持 'condense_plus_context' 或 'simple'")
        continue
    
    # 💬 执行对话
    print(f"\n🔍 使用引擎类型: {chat_engine_type}")
    if course_id:
        print(f"📚 过滤条件: course_id = {course_id}")
    elif course_material_id:
        print(f"📄 过滤条件: course_material_id = {course_material_id}")
    else:
        print("🌐 无过滤条件，搜索全部文档")
    
    print(f"💬 对话ID: {conversation_id}")
    print("\n" + "-"*50)
    print("🤖 正在思考中...")
    print("-"*50)
    

    # 🎯 获取回答
    response = chat_engine.chat(user_question)

    print(f"\n🤖 文文的回答：")
    print("="*50)
    print(response)
    print("="*50)

    # 📚 显示匹配的文本块
    if hasattr(response, 'source_nodes') and response.source_nodes:
        print(f"\n📚 检索到的匹配文本块 (共 {len(response.source_nodes)} 个)：")
        print("="*60)
        
        for i, source_node in enumerate(response.source_nodes, 1):
            content = source_node.node.get_content().strip().replace("\n", " ")
            
            if len(content) > 200:
                content_preview = content[:200] + "..."
            else:
                content_preview = content
                
            score = getattr(source_node, 'score', 'N/A')
            
            print(f"\n📄 文本块 {i}:")
            print(f"   相似度分数: {score}")
            print(f"   元数据: {source_node.node.metadata}")
            print(f"   内容预览: {content_preview}")
            print("-" * 60)
    else:
        print(f"\n⚠️  注意：此次回答未检索到相关文档片段")
        if chat_engine_type == "simple":
            print("   (simple 模式不进行文档检索，这是正常的)")
        
    